exclude: ^app/db/migrations/

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0  # Use the latest version
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-merge-conflict
      - id: check-added-large-files
      - id: check-ast
      - id: check-case-conflict
      - id: check-json
      - id: debug-statements
      - id: detect-private-key
      - id: requirements-txt-fixer

  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.5.6
    hooks:
      # Run the linter.
      - id: ruff
        args: [ --fix ]
      # Run the formatter.
      - id: ruff-format

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.16.0
    hooks:
      - id: mypy
        additional_dependencies:
          - django-stubs
          - django-environ
          - types-setuptools
          - types-requests
          - types-PyYAML
          - types-python-dateutil
          - types-pytz
          - types-six
          - types-urllib3
          - fastapi
          - pytest-asyncio
          - pytest-django
          - pytest
          - pytest-mock
          - types-beautifulsoup4
          - celery-types

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.9  # Use the latest version
    hooks:
      - id: bandit
        exclude: ^tests/
        args: ["-ll", "-r", "app"]

  - repo: https://github.com/djlint/djLint
    rev: v1.34.1
    hooks:
      - id: djlint-reformat-django
      - id: djlint-django
